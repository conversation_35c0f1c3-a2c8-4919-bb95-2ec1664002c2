import { Component } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { SupabaseService } from '../../services/supabase.service';
import { NgxImageCompressService } from 'ngx-image-compress';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-product-form',
  templateUrl: './product-form.component.html',
  styleUrl: './product-form.component.scss'
})
export class ProductFormComponent {
 productForm: FormGroup;
  previewUrl = '';
  resizedFile!: File;

    constructor(
    private fb: FormBuilder,
    private supabase: SupabaseService,
    private imgCompress: NgxImageCompressService
  ) {
    this.productForm = this.fb.group({
      title: [''],
      price: [''],
      description: ['']
    });
  }
  
  onFileChange(e: any) {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async () => {
      const img = reader.result as string;
      const compressed = await this.imgCompress.compressFile(img, -1, 50, 50);
      this.previewUrl = compressed;
      const blob = await fetch(compressed).then(r => r.blob());
      this.resizedFile = new File([blob], file.name, { type: file.type });
    };
    reader.readAsDataURL(file);
  }

  async submit() {
    if (!this.resizedFile) return alert('Upload an image first!');
    const name = `${Date.now()}-${this.resizedFile.name}`;
    const { error: upErr } = await this.supabase.uploadImage(this.resizedFile, name);
    if (upErr) return console.error(upErr);
    const url = `${environment.supabaseUrl}/storage/v1/object/public/product-images/${name}`;
    const { title, price, description } = this.productForm.value;
    await this.supabase.insertProduct({ title, price, description, image_url: url });
    alert('Product added!');
    this.productForm.reset();
    this.previewUrl = '';
  }
}
