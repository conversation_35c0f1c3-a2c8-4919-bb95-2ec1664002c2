import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { SupabaseService } from '../../services/supabase.service';
import { ProductService } from '../../services/product.service';

@Component({
  selector: 'app-product-list',
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss']
})
export class ProductListComponent implements OnInit, OnDestroy {
  products: any[] = [];
  private alive = true;
  loading = true;
  constructor(private supabase: SupabaseService,private productService: ProductService) {}

  async ngOnInit() {
    // Optional: clear only during testing
    // localStorage.clear(); sessionStorage.clear();

     try {
      this.products = await this.supabase.getProducts();
    } catch (error) {
      console.error('Failed to load products', error);
    } finally {
      this.loading = false;
    }
    
//     const res = await this.supabase.getProducts();
//     console.log('res', res);
// debugger;
//     if (!this.alive) return;

//     if (res.error) {
//       console.error('❌ Product fetch error:', res.error);
//     } else {
//       console.log('✅ Products fetched:', res.data);
//       this.products = res.data || [];
//     }
  }

  ngOnDestroy() {
    this.alive = false;
  }
}
