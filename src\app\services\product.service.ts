// src/app/services/product.service.ts
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

@Injectable({ providedIn: 'root' })
export class ProductService {
  private supabase = environment.supabaseUrl;

  // constructor(private http: HttpClient) {}
 constructor(private http: HttpClient) {
    this.supabase = createClient(environment.supabaseUrl, environment.supabaseAnonKey,{
        auth: {
        autoRefreshToken: false,
        persistSession: false,
        /**
         * Prevent LockManager errors by overriding the lock function
         */
        lock: async <R>(name: string, timeout: number, fn: () => Promise<R>): Promise<R> => {
        return await fn();
    }
    }
});
    console.log('SupabaseService created', this.supabase);
    // this.supabase.auth.onAuthStateChange((event, session) => {
    //   console.log('Auth event:', event, 'Session:', session);
    // });

  }
  // getProducts() {
  //   return this.http.get(`${this.baseUrl}/products`);
  // }

  async getProducts() {
  try {
    // Verify Supabase client is initialized
    if (!this.supabase) {
      console.error('Supabase client is not initialized');
      throw new Error('Supabase client is not initialized');
    }

    console.log('Querying products table...');
    // const { data, error } = await this.supabase.from('products').select('*');
const result = await this.supabase.from('products').select('*');
console.log('Raw result:', result);
    const { data, error } = await this.supabase
                            .from('products')
                            .select('*')

    if (error) {
      console.error('Supabase error:', error);
      throw new Error(`Failed to fetch products: ${error.message}`);
    }

    console.log('Products data:', data, 'Type:', Array.isArray(data) ? 'Array' : typeof data);
    if (!data || data.length === 0) {
      console.warn('No products found in the database');
    }

    return data;
  } catch (error) {
    console.error('Unexpected error in getProducts:', error);
    throw error;
  }
}
  addProduct(product: any) {
    return this.http.post(`${this.baseUrl}/products`, product);
  }

  // Add other methods as needed...
}
