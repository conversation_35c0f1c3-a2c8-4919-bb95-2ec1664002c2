// src/app/services/supabase.service.ts
import { Injectable } from '@angular/core';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { environment } from '../../environments/environment';


@Injectable({ providedIn: 'root' })
export class SupabaseService {
  private supabase: SupabaseClient;

  constructor() {
    this.supabase = createClient(environment.supabaseUrl, environment.supabaseAnonKey, {
          auth: {
      autoRefreshToken: false,
      persistSession: false,
      /**
       * Prevent LockManager errors by overriding the lock function
       */
      lock: async <R>(name: string, timeout: number, fn: () => Promise<R>): Promise<R> => {
      return await fn();
      }
    },
  // auth: {
  //   autoRefreshToken: true,
  //   persistSession: true,
  //   detectSessionInUrl: false,
  //   // multiTab: false  // disables lock usage
  // }
});
  }

  uploadImage(file: File, filename: string) {
    return this.supabase.storage
      .from('product-images')
      .upload(filename, file, { cacheControl: '3600', upsert: true });
  }

  insertProduct(product: any) {
    return this.supabase.from('products').insert([product]);
  }

//   async getProducts() {
//   try {
//     return await this.supabase.from('products').select('*');
//   } catch (err: any) {
//     if (err.name === 'NavigatorLockAcquireTimeoutError') {
//       console.warn('Lock error, retrying in 500ms...');
//       await new Promise(res => setTimeout(res, 500));
//       return this.supabase.from('products').select('*');
//     } else {
//       throw err;
//     }
//   }
// }
async getProducts() {
  //  debugger;
  // const hasSession = await this.waitForSession();
  // if (!hasSession) {
  //   return { error: { message: 'No session available after waiting' }, data: null };
  // }

  try {
    debugger;
    return await this.supabase.from('products').select('*');
  } catch (err: any) {
    if (err.name === 'NavigatorLockAcquireTimeoutError') {
      console.warn('Lock error, retrying...');
      await new Promise(res => setTimeout(res, 500));
      return this.supabase.from('products').select('*');
    }
    return { error: err, data: null };
  }
}

async waitForSession(retries = 5, delay = 500): Promise<boolean> {
  for (let i = 0; i < retries; i++) {
    const { data: { session } } = await this.supabase.auth.getSession();
    if (session) return true;
    await new Promise(res => setTimeout(res, delay));
  }
  return false;
}

}
